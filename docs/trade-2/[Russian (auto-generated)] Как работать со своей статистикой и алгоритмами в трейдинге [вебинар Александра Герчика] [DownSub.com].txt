[музыка]
Всем привет дорогие друзья если видно
слышно ставим плюсики поднимем вопрос
Сегодня Хорошие такие интересные темы по
одной теме Я вам приготовил слайды
вторую тему покажу на реальных графиках
поэтому пойдём будем говорить так от
простого к
сложному у нас сегодня три темы это на
самом деле вы присе много Тем я стараюсь
выбирать несколько тем сразу не од те
несколько тем сразу чтобы покрыть сразу
несколько тем А значит вопрос первый
первый принцип работы со своей
статистикой второе - это При каких
условиях делаем коррекцию личного
алгоритма И как часто и третье перенос
позиции на ноч Давайте начнём на самом
деле с самой простой темы это
корректировка личного алгоритма Давайте
объясню одну очень важную вещь
что обычно трейдеру надо какой-то
определённый промежуток времени для того
чтобы он выстроил свой алгоритм который
будет работать и какой-то промежуток
времени вы будете однозначно
приспосабливаться к рынку это может быть
например не первичный заход а вторичный
заход Например у меня есть сейчас
трейдер на программе на годовой Миша и
например мы посмотрели что первичные
заходы То есть импульсные заходы у него
не очень хорошо работают и мы его пока
перевели на вторичный заход то есть
Давайте обс одну вещь за что не нужно
переживать Дело в том что алгоритм - это
будем говорить так
такая ситуация К которой вы должны
прийти к так
называемому одностраничного алгоритму то
есть у вас алгоритм должен вписаться в
то что он будет очень простой чем
сложнее ваш алгоритм чем больше у вас
пунктов в вашем алгоритме Сейчас секунду
мама звонит
не успел чем больше пунктов в вашем
алгоритме тем тяжелее вам будет делать
сделки то есть чем больше у вас условий
в алгоритме по поводу сделки тем вам
будет тяжелее Поэтому в самом начале ваш
алгоритм должен быть будем говорить так
от сложного к простому понятно что в
самом начале вы ещё не готовы будете
сделать какой-то будем говори алгоритм
поэтому Ари вже
алгоритм алгоритм должен быть заточен на
то что когда вы делаете
сделки вы через призму алгоритма
понимаете сделки которые вы сделали К
чему вообще Давайте Элементарно подумаем
к чему мы идём мы идём к тому чтобы
каждый из вас стал самостоятельной
единицей Вы не должны быть по-простому
зависимым ни от кого там ни от нас ни от
кураторов ни от чего А смысл всего того
чему Мы вас учим Это для того чтобы вы
стали самостоятельной единице в какой
ситуации вы можете вообще стать
самостоятельной единицей Вы можете стать
самостоятельной единицей только в одной
ситуации когда вы делаете сделки когда
что-то идёт не так и вы понимаете что вы
сделали не так в ваших сделках Например
почему люди идут ко мне учиться на
программу люди идут учиться вот как
алокс написал тренер люди идут учиться к
тренеру для того чтобы тренер когда
что-то не получается Он подкрутил когда
получается тренер скажет Молодец ты Всё
правильно делаешь А когда что-то не
получается тренер должен подкрутить
Например если это теннис там поставить
руку там научить двигаться правильно то
есть смысл заключается в том что вы
сможете стать самостоятельной единице
Когда вы сможете понимать Где вы
накосячили и вы знаете Очень часто вот
даже на годовых программах даже на
программах Black очень большое
количество студентов показывают сделки
например да там отработали 2 недели и не
понимают где их кося
и алгоритм вам в этом поможет например
вы торгуете первичный пробой что самое
плохое для первичного пробоя Первое это
у вас должно быть всё прописано первое
При первичном пробое самое плохое - это
когда уровень пили то есть первичный
пробой то есть первичный импульс он
всегда должен быть сильным при двух
условиях Первое это обязательно должна
быть какая-то энергия накопления
обязательно без неё импульса не будет Ну
то есть могут быть новости но новости К
сожалению мы не знаем и вторая очень
важная вещь уровень не должен пили то
есть чем больше ваш уровень Там пилил
там неделю назад 5 дней назад тем
тяжелее будет сделать импульс Почему
Потому что идёт огромное количество
накоплений встречных лимитных ордеров на
уровне поэтому например по каждому сили
торговли у вас должен быть пока прописан
алгоритм например как вы торгуете пробои
Как вы торгуете ложные пробои и вот
Каждый раз когда вы начинаете делать
какие-то сделки Вы должны его
адаптировать в
начале естественно никакого алгоритма у
вас не будет у вас скорее всего будет
просто конспект да у вас будет просто
какое-то количественное перечисление
огромное будет какое-то количество
информации и благодаря этому вы сможете
дальше что-то выстраивать то есть
благодаря тому что у вас будет алгоритм
или конспект в котором будет всё
написано дальше Вы сможете устраивать То
есть вы должны написать что вам нужно и
чего не должно быть то есть например
если вы торгуете ложный пробой не должны
быть большие хвосты а должны быть
например большие подходы Паранормальные
бары дальние уровни дальние тесты и всё
остальное То есть вы обязательно в
алгоритме должны всё это прописать когда
вы будете уже начинать делать более
качественные сделки тогда благодаря
этому вы будете уже переходить на совсем
другой уровень и будете адаптировать то
что у вас есть
в какой-то момент у вас будет всё
нормально и по сути дела алгоритм будет
совсем простой одностраничный и вы
скорее всего в него не будете
заглядывать но пока у вас не будет
системных сделок мы сейчас плавно
перейдём на статистику и как её
правильно вести пока у вас не будет
системных сделок
вы неее
это сделки которые похожи по сути дела
одна на другую
раз это та ситуация в которой у вас
очень мало ошибок Потому что когда вы
многие начинаете торговать и у вас по
сути дела там большое какое-то
количество сделок вы в первую очередь
эти сделки делаете на каком-то
подсознательном уровне потом вы
начинаете с этими сделками работать То
есть вы не можете Многие говорят я вот
там веду сразу статистику статистику
нужно начинать только вести Когда вы
делаете системные сделки пока у вас нету
системных сделок статистику вести
абсолютно никакого смысла нет почему
потому что у вас будет раздрайв вы
будете и это торговать и это торговать
Когда вы начнёте видеть что-то одно что
вы например что-то торгуете Пусть это
будет там пробои или это ложные пробои
или ещё что-то но когда вы видите что вы
начинаете это торговать то вы в первую
очередь можете немножко начинать делать
системные сделки и вот только тогда
когда вы будете начинать делать
системные сделки А вы можете переходить
к статистике никакого смысла вести
статистику пока системные сделки не
начались вы не то что не имеете права Вы
имеете право просто смысла никакого нет
может быть в начале Когда вы торгуете
там пробой отбой ложный пробой при
условии того что вы делаете всё
правильно
У вас наверное будут какие-то
определённые правила которые вам помогут
Ну это не значит что это надо списывать
со счёта То есть я готов А понимать что
у меня какие-то вещи начинают получаться
и я смещаются в ту сторону например вот
вас сегодня 15 человек и вы знаете я
уверен что если каждого из вас спросить
вот сейчас задать вопрос да то каждый из
вас сможет ответить приблизительно что у
него лучше получается Да например я могу
сегодня спросить там Что там у вас лучше
получается там например у Эдуарда
Медведева Эдик мне скажет там Шеф у меня
лучше всего получается пробои то есть
это какие-то вещи которые на
подсознательном уровне я вам расскажу
одну очень смешную историю которая
показывает о том что статистику нужно
реально вести что она действительно
помогает в общем был у меня такой
студент Виталий Саранча и он очень любил
торговать goldman са
вообще банки это очень волатильные
инструменты по одной простой причине
потому что банки скорее всего как
индустрия наверное будем говорить так
приравнивается к нефтянке То есть это
индустрия которая очень тяжело торгуется
причина предельно проста они зависят от
большого количества экономических
параметров они зависят от Ирей они
зависят от экономического состояния
страны экономического состояния людей то
есть какая-то новость моментально
выходит ина может наоборот вознести к
высотам банковскую индустрию поэтому я
никогда не любил торговать бан расскажу
даже больше когда-то у меня была акция C
One и она мне когда-то там очень сильно
ударила Ну по тем временам Это много для
меня было там 1000 долларов притом я не
успел опомниться не успел мама сказать я
перестал е вообще торговать и никогда по
сути дела Не люби торговать бан как я
уже сказал из будем говорить их
чувствительности к рынку то есть есть
индустрии которые вообще не обращают
внимания например биофармацевтический
инструмен они очень сильно зависят от
любой новости там ближний Восток любой
конфликт там Советский канал Вы помните
Да когда там корабль этот сел на мель И
всё И там нефтянка улетела то есть или
там любая бомба взорвалась там на
Ближнем Востоке там Израиль пошёл на
Ливан там Израиль пошёл ещё на кого-то и
эти вещи очень чувствительные а есть
индустрия которая не чувствительны
поэтому това
Я был в Киеве и говорю Ну как у тебя Он
говорит всё хорошо вот я на КСЕ
зарабатываю деньги Я говорю Откуда ты
знаешь что ты на го Саксе зарабатываешь
деньги Он говорит Ну вот я же его торгую
каждый день я говорю Послушай от того
что ты торгуешь его каждый день это
абсолютно не значит что ты на нём
зарабатываешь деньги что вы думаете мы
открыли начали вести статистику с ним то
есть забили всё в статистику проверили
статистические данные и проверили одно
что
это была худшая акция в его статистике
То есть все деньги 90% денег которые там
он
потерял из убытка В смысле Ну часть
убытка 90% Это был го То есть Вам иногда
кажется что если вы что-то Правильно
делаете Это действительно правильно но
бывают такие ситуации на рынке Когда у
вас по-простому
заливается взгляд То есть вы торгуете вы
думаете что вы торгуете эту ситуацию а
на самом деле эта ситуация приносит вам
больше всего убытков поэтому надо плавно
переходить к статистику ито давайте
сейчас перед тем как мы будем говорить
перейдём к статистике Давайте завер ещё
раз алгоритм Когда вы вносите какие-то
изменения Вы должны понимать что эти
изменения Должны что-то внести в вашу
торговлю например Вы должны понимать
одну веь что есть ситуация которая к
сожалению пока не под контрольно эта
ситуация называется конъюнктура рынка
Что это значит когда на рынке какие-то
виды изменения то есть Рынок там ведёт
себя по-другому вы ничего сделать не
сможете то есть что это значит смотри
например вы начали торговать
пробои и рынок перестал быть пробой то
есть нету однобоко рынка рынок не идёт
ни вверх ни вниз а как-то очень коряво
себя ведёт вы говорите Бля у меня пробои
не работают я буду ть ложно так нельзя
сказать по одной простой причине Потому
что сейчас может быть не пробой рынок
поэтому вы должны понимать что вам всё
равно придётся возвращаться к алгоритму
и вы должны самое главное себе записать
что есть разные конъюнктуры рынка и на
разных конъюнктура рынка рынок себя
ведёт по-разному поэтому вы будете себя
вести естественно тоже по-разному это
очень важно поэтому алгоритм можно
адаптировать дописывать доводить
какие-то другие вещи но очень важно
понимать то что алгоритм - это вещь
которая может претерпеть какие-то
изменения и это абсолютно нормальное
явление так переходим теперь к
статистике я вам специально привёл
статистику живую Одного из наших
студентов Андрея порочен Итак смотрите
внимательно Да Первая это это реальная
статистика А вы можете вести статистику
Сейчас я вам покажу где можно
вести это статистика трейдера Видите вот
статистика трейдера Web maret ST Вы
можете например там Взять её Ну то есть
написать
а и сказать что там вы студент герчика и
вам дадут а скидку очень хорошая
статистика там глубокая всё есть вы всё
можете смотреть очень всё удобно сделать
вы сейчас её увидите будем говорить в
таком в красивом виде То есть вы должны
вот здесь вот видите Здесь всё есть там
и nce есть и Forex есть и крипта есть то
есть вы можете просто забивать сделки
теперь посмотрите что вы будете видеть в
первозданном виде Итак смотрите
внимательно это это то что вам будет на
выходе даваться Итак посмотрите
внимательно что мы здесь видим да мы
видим видите результаты по часам входа
Итак Да вы видите конечно очень крутая
доходность 400п это всё правда это
реальная доходность а человек торгует
только фьючерсы и сейчас вы увидите
когда вы уже начнёте торговать
определённые вещи по параметрам то есть
статистически то есть системно тогда вы
можете начинать шить статистику
статистика вам всё покажет например вот
Давайте посмотрим так вот всего сделок
было 211 это очень хорошая статистика
всё что больше 100 сделок это очень
хорошая статистика дальше среднее время
удержания средняя убыточная сделка
средняя прибыль
мы уже видим что видите у трейдера вот
здесь вот минимум 3 к од сделка
максимальный убыток серии сделок
максимальная просадка видите Да и теперь
дальше идём смотрите результат по дням
недели пятница
о в принципе этого ещё пока маловато
чтобы не торговать в пятницу но если
сделок например будет 400 500 да то есть
например Давайте посмотрим сколько
трейде торговал и мы сейчас пойм с
декабря по май у него Прошло 5 месяцев 6
месяцев достаточно ли этой статистики
чтобы утверждать что пятница убыточны
ответ да Если вы на протяжении 6 месяцев
то есть ну только при условии что это не
разовая потеря в пятницу если вы на
протяжении 6 месяцев теряете деньги по
пятницам видите Здесь всё чётко здесь
обмануть нельзя Не то что нельзя
невозможно вот эти цифры это голые цифры
выбитые на асфальте их возможно убрать
То есть вы видите что пятница убыточная
Вот она видите пятница убыточная а то
что среда там положительная Это ничего
не значит но факт тот что пятница
убыточная Вы знаете я вам расскажу одну
тоже очень смешную вещь Парадокс когда я
очень агрессивно
торговал у меня пятница была около нуля
Что это значит я пятнице просто выды и я
перестал на работу ходить в пятницу
когда я увидел что пря живая статистика
потому что сделку у меня было очень
много я увидел что пятница там у меня Ну
около нуля там может какая-то легко
Плюсовая я перестал ходить пятницу на
работа потому что я копил энергию на
следующей неделе и действительно когда я
убрал пятницу то 4 дня в неделю у меня
было больше энергии и результат был
лучше то есть в данной ситуации за
полгода пятница это реальный счёт это
реальный счёт в на Ютюбе есть интервью с
этим трейдером зовут его Андрей порочен
То есть это не демо ничего у него
действительно черный аккаунт и всё
остальное Профит фактор очень хороший
Профит фактор вообще Профит фактор всё
что выше полутора считается очень и
очень неплохо идём дальше видите все
инструменты хорошие масло юрс snp золото
нефть и НКС да то есть по сути дела На
всех фьючерсах Он зарабатывает деньги то
есть у него нету инструментов в данной
ситуации Если вы будете на каком-то
инструменте терять деньги оно вам сразу
покажет чтобы не забыли СГ дальше
посмотрите внимательно результат по
часам входа вот чётко всё видно Видите
вот любое после 12 до 13 часов он теряет
деньги вот хоть убей конечно Тут
надо смотреть более развёрнутую
статистику она есть более глубокая
статистика тут конкретно видно Да почему
то есть результат по часам входа это
невозможно объяснить и это не надо
пытаться объяснить факт тот что
статистика показывает что ну
довольно-таки прилично Если вы возьмёте
1 2 3 4 5 6 результатов по часа входа то
здесь результат хуже чем другие 6 часов
Ну то есть Прямо отрицательный результат
то есть и вы должны понимать
что в это время что-то происходит не то
на рынке видите То есть если статистика
вам показывает глубокое что что-то на
рынке происходит не то вы должны на это
обратить внимание дальше как я уже
сказал все инструменты на всех
инструментах Он зарабатывает то есть всё
нормально и дальше топ метода входа PR -
это пробой это пробой это пробой прочее
ЛП то есть видите у трейдера работают
только пробои пока всё зелёное
переживать не за что пробой у него видно
какие-то три модели видите пробой три
пробой оди пробой 2 То есть он у него
три пробойная модели где он торгует
только пробой прочее Я не знаю что это
ЛП - это ложный пробой притом два видите
какой-то у него ложный пробой может
быстро ложны пробой может ложны пробой
одним баром то есть в данной ситуации
видите как только вы начнёте дено
количество сделок статистика вам покажет
что что-то так или что-то не так то есть
очень многие
люди совершают одну очень большую
проблему они пытаются усиливать вот эти
факторы это в корне не правильно надо
усиливать всегда свои сильные стороны а
слабые стороны они будут подтягиваться
То есть кто-то может наверно из вас 17
человек залу и сказать
Ага пятница убыточная Я сейчас буду
бороться за эту пятницу от простой
вопрос
Нахуя не нужно это если вы видите что у
вас там с 12 до часу дня вы теряете
деньги и вы смотрите количество сделок
например вы пошли сюда и у вас 80%
обычны убыточных сделок
в в этот промежуток зачем здесь вот это
маленький такой убыток ничего там
страшного нет может Это случайность но
здесь прям видимый убыток видите это
доллары Не забывайте то есть и при его
там риске на сделку 300 долларов он в
это время там потерял 2 поя Да стопов
значит что-то не то это не значит что
надо все сделки стараться делать здесь
То есть вы чётко понимаете где красное
туда на этот момент нужно обратить
внимание пробои хорошо работают Нахуя
нужно лезть в ложные пробои Если они у
тебя заберут энергию время и деньги то
есть каждый раз когда вы не обращаете
внимания на эти косяки вы убираете
концентрацию с этих сильных
положительных сделок то есть очень важно
концентрироваться на том что у вас
получается и на том что у вас не
получается то что не получается не надо
пытаться там изменить починить и всё
остальное надо просто на это не обращать
внимания дальше Вот эта Сводная таблица
очень хорошая в которой всё можно видеть
видите графики доходов по месяцам Но вот
вот сюда первый очередь мы будем
обращать внимание на что мы обращаем
внимание смотрите результат без комиссии
гроз
36.000 результат общий нет 34.000 то
есть 2.400 долларов было потрачено на
комиссию это очень хорошая цифр то есть
если вы на комиссии тратите меньше
10 про вашего гросса это очень хорошая
цифра видите прибыльных сделок не так
много но у него соотношение риск прибыль
больше чем 3: одно он даже больше Я
просто как бы знаю у него он берёт йк
минимум 5 к одному и эти ть к одному у
него получились благодаря статистике То
есть он стал видеть что каждый раз когда
он например сидит в сделке или выходит
из сделки инструмент даёт ть к одному
очень важно это понимать и вы должны к
этому прийти то есть видите у него
статистика прибыльных шортов сделок 31
прибыльных лонго сделок
Да но у него видите соотношение риск
прибыль очень хорошее То есть это за
минусом комиссии за минусом всего
остального видите средне математическая
сделка максимально прибыльная сделка
максимально убыточная вот тут отже тоже
Великолепная цифра видите 5% и 1,2 то
есть здесь цифр у нас получает сколько
где-то 4 к одному то есть что это значит
он везде статистику очень хорошую
выбирает то есть видите у него есть
убытки это нормально убытков боятся не
надо смотрите дальше очень хорошая серия
максимальная серия прибыльных сделок
максимальная серия убыточных сделок даже
учитывая то что у него была максимальная
серия убыточных сделок в два раза больше
тоже
бывает он не потерял те деньги которые
он заработал То есть если Вы посмотрите
здесь действительно Великолепная
глубокая статистика которая вам
показывает что трейдер не нарушает
правила у него чёткая статистика у него
нету никаких взрывов на макаронной
фабрике ВС очень красиво а теперь
Посмотрите я вам покажу На мой взгляд
лучшую картинку которую я хочу вам
показать Итак смотрите внимательно это
май д первого года было сделано 33
трейда посмотрите внимательно всего 27
сделок
прибыли но соотношение риск прибыли
очень хоро посмотрите
внимательно всё равно Заработал деньги
доходы с депозита
29% То есть если вы будете работать с
очень высоким соотношением риск прибыль
то даже там при 28% положительных сделок
всё будет нормально Теперь смотрите цель
факт видите удельный вес прибыльных
сделок обычно у него 40 а факт 27 Профит
фактор видите упал в два раза и такая
статистика может прибить несколько
месяцев но факт тот плюс очень низкий
фактор восстановления видите поэтому не
надо бояться убыточных сделок надо
выбирать прибыль здесь это можете
сфотографировать можете все сделки
проверить видите
Посмотрите здесь все сделки это реальные
все сделки то есть можете просто себе
сфотографировать экран я сделаю побольше
можете все сделки сейчас ещё побольше
сделаю можете все сделки проверить это
все сделки реальные видите Какое
количество сделок Какой объём видите в
какие-то дни пять сделок в какие-то дни
три сделки Вот можете посмотреть видите
и потом можете просто посмотреть что и
как теперь дальше
идём смотрите топ-10 методов хода видите
всё
ЛП даёт отрицательные деньги Лучше не
лезть Вы зададите наверное вопрос потому
что я до этого когда возвращался по
поводу алгоритма Я сказал что делать
когда здесь плохо может быть рынок
сейчас
поэтому не надо сейчас концентрироваться
Если вдруг вот эти цифры начнут падать
тогда действительно может и стоит оттуда
свалить идём дальше статистика отработки
сценариев Как ни странно посмотрите
внимательно он ведёт сценарий видите
очень внимательно подтвердилось
направление потенциальный результат
могла быть твх и убыточные сделки с
неработа сри То есть это очень глубокая
ста
чале Вы можете сделать очень много
кастомизированные Вы можете добавить
свои окошки Вы можете добавить свои
сделки запомните одну очень важную
вещь сделки - Это ваши лучшие учителя
сделки - Это ваши лучшие учителя и
Только благодаря вашим сделкам вы
сможете научиться поэтому очень важный
фактор очень важный фактор это понимать
свои собственные сделки не просто
понимать свои собственные сделки А
понимать свои собственные сделки на
предмет анализа пока презентации Света
никакой нету я её уже убрал она уже
закончилась Сейчас будет другое Поэтому
очень важно Очень важно понимать как
себя ведёт
инструмент очень важно понимать
статистику с которой вы работаете Это
очень важ ве ива понима
что очень многие люди не обращают на это
внимание а конюнктура рынка это очень и
очень важно Теперь давайте перейдём к
третьей заключительной части
нашего вебинара это перенос позиции на
ночь Итак смотрите внимательно первое
Когда вы оцениваете перенос позиции на
ноч первое что мы долж видеть это вот
эту вот ситуацию
вчера спай упал Сегодня он идт на
обновление Хая значит мы должны смотреть
инструменты которые по-простому ведут
себя сильнее Как понять сильнее то есть
не было вот этого большого бара и
инструмент выглядит сильнее вот один из
этих инструментах первый это кавана
смотрите видите то есть кавана всё время
держится возле ха дальше ха
1561 кавана уже практически
видите сюда
Даше идти вверх что делается в такой
ситуации смотрите здесь вот есть только
один бар ха
15761 Если рвано сможет закрыться Да
давайте сделаю шаг
назад для переноса позиции на ночь самое
важное это первое инструмент должен быть
сильнее либо слабее рынка это раз второе
инструмент должен обязательно очень
хорошо закрываться
то есть что такое закрытие дневки Да вы
можете сказать Ну может же такая
быть может от этой
херния падения не застрахован никто
включая меня то есть если рвано сможет
вот здесь вот закрыться чуть-чуть выше
то у карван появляется очень большой
шанс для того чтобы идти выше и мы
сейчас многие инструменты вместе с вами
посмотрим NVIDIA
смотрите 130 долларов по н види шикарный
уровень То есть если nidia видите выход
из канала То есть если
N пробивает 130 долларов вверх и
закрывается в любом месте выше 130 в
любом даже там 130 05 или
13010 это является логом сигналом то
есть очень важно первое Где инструмент
закрылся энергия захода То есть если вы
видите
что закрыться 130 долларов или чуть-чуть
выше это очень сильный сигнал для жения
движения это показывает что она очень
сильна и вот так вот вы должны идти по
каждому инструменту пока вы не увидите
что инструмент например вот смотрите
сегодня видите рынок очень сильно пошёл
вверх сегодня плю 1% А этот инструмент
Уже показывает свою слабость запишите
pltr то есть если инструмент выйдет вот
сюда вот вниз L 3182
Да этот инструмент можно шортить Почему
Потому что он стоит слабее рынка То есть
он должен показать свою слабость если он
закроется вот где-то здесь пока ещё
слабости нет но он уже покажет свою
слабость потому что рынок + 1% он если
бы даже был + 1% было бы уже хорошо И
вот так вот вы должны идти по каждому
инструменту до тех пор пока вы не
увидите Видите вот смотрите сегодня азон
сегодня слабее рынка Почему рынок уже
перекрыл давайте ещ раз покажу видите
рынок перекрыл вчерашнее падение вот оно
видите А азон где Нан вот он азон не
может перекрыть вчерашнее падение это
уже показывает его слабость да то есть
не забывайте одну очень важную вещь
деньги постоянно мигрируют постоянно
мигрируют с одного сектора в другой с
одной акции в другую И когда вы видите
что инструмент показывает свою слабость
скорее всего что в этом инструменте в
первую очередь есть проблема и на эту
проблему нужно обращать внимание Тесла
видите тоже ещё не перекрыла движение
Значит она более слабая то есть ещё раз
это накопление это закрытие инструмента
под Хай или ло В конце дневки это
Желательно чтобы Впереди у инструмента
Не было никакого сопротивления И вот так
вот ваша задача идти по каждо
инструменту до тех пор пока вы вот
отлично видите люфт у были хорошие
квартальные отчёты Вот видите вчера он
хорошо закрылся выдержал сильное падение
вот у лифта есть новый очень сильный
уровень там вот
1182 естественно если мы сможем хорошо
закрыться то мы можем увидеть хорошие
лонги Мета верите не смогла сегодня
ничего сдела netfx тоже самое смотрите
вообще видите netfx идт ниже это
показывает его слабость очень важно
чтобы вы понимали очень важные вещи в
каких ситуациях можно выходить из
позиции То есть если ситуация видите как
с netflix ситуация показывает то что
netflix не готов пока идти вверх Почему
А netflix META там carvana Tesla - это
большие
компании и если на рынке появляется вот
эта вот денежная масса которая начинает
толкать рынок и эти акции не идут вверх
Это значит что деньги сюда не зашли
видите что она сделала она сделала
показывая то что в инструменте будет
скорее всего падение вы сразу
идентифицирует вот у неё уровень 690 и
если инструмент вот он ровно 690 если
инструмент начинает падать ниже 690 это
очень сильный сигнал для шорта то есть
очень важно понимать видите Наки тоже
подходит сюда очень важно понимать в
каких ситуациях можно переносить позицию
на ноч смотрите очень внимательно
объясню вам одну очень важную вещь
ничего страшного в переносе позиции на
ночь нет И у вас у многих сразу
возникает вопрос Это вопрос гепа
Да не забывайте одну очень важную вещь а
что если вы зайдёте очень маленьким
объёмом одну акцию пять акций это
единственный вариант как вы будете
учиться
в мои молодые годы трейдинговые
я
тоже но единственный вариант переноса
позиции на ночь единственный вариант это
когда вы берёте небольшую позицию и
заходите Вы можете смотреть Вы можете
наблюдать но запомните одну вещь я
всегда говорил и в Польше последний раз
на семинаре
сказал Если вы не торгуете акцию
физически Это то же самое что смотреть
порнофильм в котором вы не участвуете
Поэтому если вы хотите оставить
инструмент но у вас в алгоритме должны
быть прописаны
А у вас в алгоритме должны быть
прописаны ситуации в каком случае Вы
оставляете позицию на ночь и если у вас
всё сходится Можете просто по одной там
по пять акций зайти и быть
там всех очень люблю На мой взгляд
получился очень хороший
Аа динамичный вебинар покрыли все темы
всем хорошего дня Кто кстати в Турцию
едут - Поставьте плюсики кто едут в
Турцию с нами в Турции увидимся мой
совет один очень
важный вебинар пересматривает всегда с
ручкой бумажкой Я понимаю что когда я
говорю не всегда А можно всё успеть но
обязательно пересматривать
вебинар обязательно и будет всё хорошо
всё всех люблю Мирного неба над головой
и всем удачи пересматривать вебинар